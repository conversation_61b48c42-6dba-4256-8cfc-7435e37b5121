.ds-accordion {
    &__container {
        @apply relative border-b border-gray-200 break-inside-avoid;
    }
    &__button {
        @apply w-full py-3 lg:py-5 text-left;
        &--inner {
            @apply flex items-center justify-between px-2 lg:px-4 space-x-6;
            &--title {
                @apply block text-3xl lg:text-5xl font-heading uppercase font-bold text-blue-850;
            }
            &--location {
                @apply block text-sm md:text-base lg:text-lg text-blue-850;
            }
        }
        &--icon {
            @apply text-xl md:text-2xl lg:text-3xl duration-200 ease-in-out transform text-gray-400;
        }
    }
    &__content {
        @apply relative overflow-hidden transition-all max-h-0 duration-500;
        &--inner {
            @apply px-2 lg:px-4 pb-6 lg:pb-10 pt-4;
        }
    }
    ul {
        @apply space-y-1;
        li {
            @apply flex items-start text-blue-850;
            i {
                @apply mt-1.5 mr-3;
            }
            p {
                @apply font-bold lg:text-lg;
            }
        }
    }
}