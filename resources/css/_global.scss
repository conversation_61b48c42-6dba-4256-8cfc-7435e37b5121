[x-cloak] {
    display: none !important;
}
@layer components {
    /*Spacing*/
    .ds-site-padding {
        @apply relative mx-auto px-4 sm:px-6 lg:px-8;
    }
    .ds-section-padding {
        @apply relative py-20 md:py-24 lg:py-28; 
    }
    .ds-section-padding-t {
        @apply relative pt-20 md:pt-24 lg:pt-28; 
    }
    .ds-section-padding-b {
        @apply relative pb-20 md:pb-24 lg:pb-28; 
    }
    .ds-site-grid {
        @apply md:grid md:grid-cols-12 md:gap-6;
    } 
    // .ds-section-full-width {
    //     @apply -mx-40;
    // }
    .dark {
        @apply text-neutral-100;
        .text-block {
            @apply prose-p:text-neutral-100 prose-strong:text-neutral-100 prose-headings:text-neutral-100 prose-ul:text-neutral-100;
        }
        h4, .h4, h5, .h5, h6, .h6 {
            @apply text-white;
        }
        .eyebrow {
            @apply after:bg-[#E1DFE3];
        }    
    }
     /*Typography*/
     h4, .h4, h5, .h5, h6, .h6 {
        @apply font-heading text-sm mb-8 text-sage-800;
     }
    .eyebrow {
        @apply relative text-center uppercase after:absolute after:-bottom-2 after:left-1/2 after:-translate-x-1/2 after:w-[42px] after:h-[2.5px] after:bg-[#938E97];
    }    
     h1, .h1, h2, .h2, h3, .h3 {
        @apply font-heading text-sage-800;
     }
     .bold-text {
        @apply font-bold text-neutral-600;
     }
    .text-block {
        @apply
        max-w-full  
        prose 
        prose-p:text-neutral-600 
        prose-p:leading-relaxed
        prose-p:first:mt-0
        prose-strong:text-neutral-600 
        prose-a:duration-200 
        prose-a:cursor-pointer
        hover:prose-a:text-sage-500  
        prose-ul:leading-relaxed 
        prose-ul:md:text-lg
        prose-ul:pl-0
        prose-ul:ml-4
        prose-ul:text-neutral-600  
        prose-li:marker:text-sage-500
        prose-li:pl-0 
        prose-li:my-0 
        prose-lead:text-neutral-600
        prose-headings:text-neutral-600 
        prose-headings:font-black
        prose-headings:mt-0 
        prose-h2:text-5xl 
        prose-h2:lg:text-6xl 
        prose-h2:mb-3 
        prose-lead:font-sans
        prose-lead:font-semibold
        prose-lead:text-lg 
        prose-lead:lg:text-xl 
        prose-figcaption:text-base 
        prose-figcaption:text-gray-450
    }
}
@layer utilities {
    .paused {
        animation-play-state: paused;
    }
    .my-rotate-y-180 {
        transform: rotateY(180deg);
    }
    .preserve-3d {
        transform-style: preserve-3d;
    }
    .backface-hidden {
        backface-visibility: hidden;
    }
}

//Forms 
form {
    input[type="file"] {
        @apply pl-1.5;
    }
    div.error {
        @apply text-red-600 text-sm pt-2;
        &:before {
            content: "\10f06a"; /* Unicode for the duotone primary layer */
            font-family: "Font Awesome 5 Duotone";
            font-weight: 900; /* Adjust if needed */
            font-size: 0.85rem;
            margin-right: 4px;
            margin-top: 2px;
            color: red; /* Primary layer color */
        }
    }
}
.custom-form select {
    @apply block w-full rounded-md border-0 py-1.5 text-neutral-700 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-amber-850 sm:max-w-xs sm:text-sm sm:leading-6;
}

//Tables 
.custom-table {
    @apply min-w-full divide-y divide-neutral-300;
    tbody {
        @apply divide-y divide-neutral-200;
        tr {
            td {
                @apply whitespace-nowrap py-4 first:pl-0 first:pr-4 first:text-lg first:text-amber-1000 last:whitespace-normal px-4 text-base text-neutral-500
            }
        }
    }
}

.social-link {
        @apply flex items-center justify-center rounded-full h-12 w-12 text-white text-xl bg-blue-850 border-2 border-white/20 hover:bg-white/20 duration-200;
    }

.fancybox__slide {
    @apply px-4 sm:px-6 lg:px-8;
}
.fancybox__content {
    @apply p-5 lg:p-10;
}

@layer base {
  .font-outline-red {
    -webkit-text-stroke: 1.5px #6E041C;
  }
    .font-outline-blue {
    -webkit-text-stroke: 1.5px #0E113D;
  }
}