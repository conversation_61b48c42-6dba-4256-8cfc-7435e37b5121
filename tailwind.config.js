/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './resources/**/*.antlers.html',
        './resources/**/*.antlers.php',
        './resources/**/*.blade.php',
        './resources/**/*.vue',
        './content/**/*.md',
    ],

    theme: {
        container: {
            center: true,
        },
        extend: {
            aspectRatio: {
                '4/3': '4 / 3',
                '3/2': '3 / 2',
                '3/4': '3 / 4',
            },
            fontFamily: {
                heading: ['bebas-neue-pro', 'sans-serif'],
                heading_semiexpanded: ['bebas-neue-pro-semiexpanded', 'sans-serif'],
                heading_expanded: ['bebas-neue-pro-expanded', 'sans-serif'],
                sans: ['Roboto', 'sans-serif'],
            },
            colors: {
                red: {
                    650: '#DF1E26',
                    850: '#6E041C',
                },
                blue: {
                    75: '#f3f5fa',
                    550: '#3156A3',
                    650: '#173A82',
                    750: '#161a57',
                    850: '#0E113D',
                },
                 yellow: {
                    150: '#E1E0AD',
                    250: '#d2d19d',              
                },
                sage: {
                    500: '#5E6F64',
                    800: '#3D4841',
                },
                gray: {
                    450: '#7B7781',
                    950: '#181E2D',
                },
                teal: {
                    75: '#eef4f3',
                    650: '#58A99D', 
                },
               amber: {
                    850: '#8E340F', 
                    1000: '#3C3329', 
                },
                green: {
                    750: '#106B64',
                },
                stone: {
                    75: '#FAF9F5',
                }, 
              },
              keyframes: {
                pulse: {
                    '0%, 100%': {
                        opacity: '1'
                    },
                    '50%': {
                        opacity: '0.5'
                    },
                },
                'loop-scroll': {
                    from: {
                        transform: 'translateX(0)'
                    },
                    to: {
                        transform: 'translateX(-100%)'
                    },
                },
                'zoom-in': {
                    from: {
                        transform: 'scale(1)'
                    },
                    to: {
                        transform: 'scale(1.1)'
                    },
                },
                'mask-fade-up': {
                    '0%': {
                        opacity: '0',
                        transform: 'translateY(12rem)',
                        visibility: 'visible',
                    },
                    '100%': {
                        opacity: '1',
                        transform: 'translateY(0)',
                        visibility: 'visible',
                    },
                },
                'in-from-left': {
                    '0%': {
                        opacity: '0',
                        width: '0',
                        visibility: 'visible',
                    },
                    '100%': {
                        opacity: '1',
                        width: '100%',
                        visibility: 'visible',
                    },
                },

            },
            animation: {
                pulse: 'pulse 5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                'loop-scroll': 'loop-scroll 30s linear infinite',
                'zoom-in': 'zoom-in 20s ease forwards',
                'mask-fade-up': 'mask-fade-up 1s ease forwards',
                'in-from-left': 'in-from-left 1s ease forwards',
            },
        },
    },

    plugins: [
        require('@tailwindcss/typography'),
        require('@tailwindcss/forms'),
        require('tailwindcss-3d')({
            legacy: true
        }),
        require("tailwindcss-animation-delay"),
    ],
};
