.ds-header {
        @apply absolute w-full py-3.5 md:py-2.5 z-50 duration-100;
        @media (min-width: 1280px) {
            padding-top: 1.19rem;
            padding-bottom: 1.19rem;
        }
        .hamburger-line {
            @apply bg-white;
        }
        .master-logo {
            display: block !important;
            @apply w-[90px] lg:w-auto
        }
        .white-logo {
            display: none !important;
        }
        .nav-holder {
            @apply flex items-center justify-between space-x-6;
        }
        .ds-hamburger-icon {
                @apply -mt-12;
        }
        .desktop-menu {
            li {
                @apply flex flex-col items-start;
                a, button {
                    @apply font-heading_expanded tracking-wider text-white hover:text-gray-200 uppercase text-base duration-200 drop-shadow-[0_2px_4px_rgba(0,0,0,0.55)];
                }
                .underline {
                    @apply block h-px bg-red-700 w-0 duration-200 ease-in-out;
                }
                .active-nav-item {
                    @apply text-white;
                    .underline {
                        width: 100% !important;
                    }
                }
            }
        }
        &.active {
            @apply fixed top-0 left-0 py-3.5 bg-white z-50 shadow-sm;
            .white-logo {
                display: block !important;
            }
            .master-logo {
                display: none !important;
            }
            .desktop-menu {
                li {
                    @apply flex flex-col items-start;
                    a, button {
                        @apply text-blue-850 hover:text-blue-850 drop-shadow-none;
                    }
                    .underline {
                        @apply bg-blue-850;
                    }
                    .active-nav-item {
                        @apply text-blue-850;
                    }
                }
            }
            .ds-hamburger-icon {
                @apply mt-0;
            }
            .hamburger-line {
                @apply bg-blue-850;
            }
        }
    .mobile-menu {
        ul {
            @apply mt-12 space-y-3 md:mt-0 md:space-y-4 flex-grow;
            li {
                @apply overflow-hidden;
                svg {
                    @apply text-red-650 w-4 h-4;
                }
                a, button {
                    @apply relative inline-flex items-center text-white hover:text-gray-300 duration-200 ease-in-out opacity-0 animate-mask-fade-up animation-delay-100 font-heading_expanded uppercase tracking-wider text-lg w-full;
                    .animated-icon {
                        @apply text-base font-medium duration-200 -translate-x-4 md:text-lg md:-translate-x-6 mr-2 text-red-650 -mt-1;
                    }
                    .text {
                        @apply text-2xl font-medium duration-200 -translate-x-4 md:text-3xl md:-translate-x-6;
                    }
                    .chevron {
                        @apply absolute right-0 text-xl text-gray-300/70 duration-200 ease-in-out;
                    }
                    &.group:hover {
                        .animated-icon {
                            @apply translate-x-2;
                        }
                        .text {
                            @apply text-red-650 translate-x-2;
                        }
                        .chevron {
                            @apply text-red-650;
                        }
                    }
                }
            }
        }
        .mobile-submenu {
            @apply mt-2 py-3 space-y-3;
            li {
                a {
                    @apply capitalize text-lg font-medium;
                    span {
                        @apply w-6 text-gray-400 text-sm -mt-1;
                    }
                }
            }
        }
        .divider {
            @apply h-px opacity-0 bg-neutral-300/20 animate-in-from-left animation-delay-100;
        }
    }
}