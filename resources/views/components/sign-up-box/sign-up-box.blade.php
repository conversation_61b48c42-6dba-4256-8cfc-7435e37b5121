@props([])
<div id="mc_embed_shell"> 
    <div id="mc_embed_signup" x-data="SignUpBox" data-module="ds-sign-up-box" {{ $attributes->merge(['class' => 'ds-sign-up-box']) }} >
        <form action="https://iggysfund.us10.list-manage.com/subscribe/post?u=28b4f274b7f81be1852c86ede&amp;id=18500323fd&amp;f_id=004445e4f0" method="post" id="mc-embedded-subscribe-form" name="mc-embedded-subscribe-form" class="validate" target="_self" novalidate="">
            <div id="mc_embed_signup_scroll" class="lg:flex lg:space-x-1 space-y-2 lg:space-y-0">
                <div class="mc-field-group flex items-center">
                    <label for="mce-EMAIL" class="sr-only">Email Address</label>
                    <i class="fa-light fa-envelope"></i>
                    <input type="email" name="EMAIL" class="required email field-entry w-[300px]" id="mce-EMAIL" placeholder="Enter your email address" required="" value="">
                </div>
                <div id="mce-responses" class="clear">
                    <div class="response" id="mce-error-response" style="display: none;"></div>
                    <div class="response" id="mce-success-response" style="display: none;"></div>
                </div>
                <div aria-hidden="true" style="position: absolute; left: -5000px;">
                    <input type="text" name="b_28b4f274b7f81be1852c86ede_18500323fd" tabindex="-1" value="">
                </div>
                <div class="clear">
                    <input type="submit" name="subscribe" id="mc-embedded-subscribe" class="ds-button ds-button--white-outline" value="Subscribe">
                </div>
            </div>
        </form>
    </div>
</div>
