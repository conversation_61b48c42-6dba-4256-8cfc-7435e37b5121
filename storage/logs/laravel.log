[2025-03-13 10:21:34] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/StopImpersonating.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\StopImpersonating->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/DisableFloc.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\DisableFloc->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/CheckMultisite.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckMultisite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/CheckComposerJsonScripts.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckComposerJsonScripts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/PoweredByHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\PoweredByHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#60 {main}
"} 
[2025-03-13 10:21:36] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#21 {main}
"} 
[2025-03-13 10:21:43] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/StopImpersonating.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\StopImpersonating->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/DisableFloc.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\DisableFloc->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/CheckMultisite.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckMultisite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/CheckComposerJsonScripts.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckComposerJsonScripts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/statamic/cms/src/Http/Middleware/PoweredByHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\PoweredByHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#60 {main}
"} 
[2025-03-13 10:21:44] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#21 {main}
"} 
[2025-04-05 14:36:49] local.ERROR: The "--timeout" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--timeout\" option does not exist. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Input/ArgvInput.php:220)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Input/ArgvInput.php(147): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('timeout', '600')
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Input/ArgvInput.php(82): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--timeout=600')
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Input/ArgvInput.php(71): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--timeout=600', true)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Input/Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Command/Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Application.php(1047): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Application.php(316): Symfony\\Component\\Console\\Application->doRunCommand(Object(Statamic\\Console\\Commands\\Install), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/symfony/console/Application.php(167): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1203): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/leflamant/artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-08-13 12:41:23] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/StopImpersonating.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\StopImpersonating->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/DisableFloc.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\DisableFloc->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckMultisite.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckMultisite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckComposerJsonScripts.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckComposerJsonScripts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/PoweredByHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\PoweredByHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#60 {main}
"} 
[2025-08-13 12:51:33] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/StopImpersonating.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\StopImpersonating->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/DisableFloc.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\DisableFloc->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckMultisite.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckMultisite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckComposerJsonScripts.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckComposerJsonScripts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/PoweredByHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\PoweredByHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#60 {main}
"} 
[2025-08-13 12:51:34] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#21 {main}
"} 
[2025-08-13 12:56:24] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/StopImpersonating.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\StopImpersonating->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/DisableFloc.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\DisableFloc->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckMultisite.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckMultisite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckComposerJsonScripts.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckComposerJsonScripts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/PoweredByHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\PoweredByHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#60 {main}
"} 
[2025-08-13 12:56:25] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#21 {main}
"} 
[2025-08-13 12:56:27] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(172): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/StopImpersonating.php(12): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\StopImpersonating->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/DisableFloc.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\DisableFloc->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckMultisite.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckMultisite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/CheckComposerJsonScripts.php(14): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\CheckComposerJsonScripts->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/statamic/cms/src/Http/Middleware/PoweredByHeader.php(18): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Statamic\\Http\\Middleware\\PoweredByHeader->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1188): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#59 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#60 {main}
"} 
[2025-08-13 12:56:27] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:83)
[stacktrace]
#0 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Support/helpers.php(380): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(931): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#6 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('encrypter', Array)
#9 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1112): Illuminate\\Foundation\\Application->make('encrypter')
#10 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(1022): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(973): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(815): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1046): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Container/Container.php(751): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1028): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(258): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(216): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1190): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 /Users/<USER>/Library/CloudStorage/Dropbox/Sites/dbd2026/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#20 /Applications/Herd.app/Contents/Resources/valet/server.php(167): require('/Users/<USER>')
#21 {main}
"} 
