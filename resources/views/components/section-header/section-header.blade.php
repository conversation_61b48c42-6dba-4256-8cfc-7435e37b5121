@props([
    'eyebrow' => '',
    'outline_colour' => 'red',
    'other_classes' => '',
    'title' => '',
])
<div x-data="SectionHeader" data-module="ds-section-header" {{ $attributes->merge(['class' => 'ds-section-header']) }}>
    @if($eyebrow)
    <p class="ds-section-header__eyebrow">{{ $eyebrow }}</p>
    @endif
    <h2 class="ds-section-header__title !leading-none font-outline-{{ $outline_colour }} {{ $other_classes }}">{!! $title ?? '' !!}</h2>
    <p class="text-base md:text-lg">
        {{ $slot }}
    </p>
</div>