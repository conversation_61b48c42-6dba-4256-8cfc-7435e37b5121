import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
// import vue2 from '@vitejs/plugin-vue2';

export default defineConfig({
    plugins: [
        laravel({
            server: {
                host: 'dbd2026.test',
                port: 5173,
                strictPort: true,
                hmr: {
                    host: 'dbd2026.test',
                },
                cors: {
                    origin: 'https://dbd2026.test',
                    credentials: true,
                }
            },
            input: [
                'resources/css/app.css',
                'resources/js/app.js',

                // Control Panel assets.
                // https://statamic.dev/extending/control-panel#adding-css-and-js-assets
                // 'resources/css/cp.css',
                // 'resources/js/cp.js',
            ],
            refresh: true,
        }),
        // vue2(),
    ],
});
