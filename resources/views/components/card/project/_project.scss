.ds-cards--project {
    @apply relative bg-cover bg-no-repeat bg-center duration-200 cursor-pointer overflow-hidden lg:hover:shadow-lg hover:shadow-neutral-500/10;
    &__image {
        @apply aspect-3/4 object-cover w-full h-full duration-500 ease-in-out transition group-hover:scale-110;
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        -webkit-transform-style: preserve-3d;
        transform-style: preserve-3d;
    }
    &__bg-gradient {
        @apply block absolute z-10 w-full h-full inset-0 bg-gradient-to-t from-black to-transparent opacity-60 group-hover:opacity-80 duration-500;
    }
    &__content {
        @apply px-6 absolute w-full bottom-6 z-10 py-0;
    }
    &__content-inner {
        @apply font-sans w-full text-white group-hover:text-stone-100;
    }
    &__title {
        @apply font-heading text-center text-3xl font-medium mb-2;
    }
    &__description {
        @apply font-heading text-center text-xl lg:text-2xl duration-500;
    }
    &__badge {
        @apply relative;
    }
}