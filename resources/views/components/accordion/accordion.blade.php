@props([
    'item' => [
        'month' => '',
        'date' => '',
        'day' => '',
        'title' => '',
        'location' => '',
        'content' => '',
    ],
])

<div data-module="ds-accordion" {{ $attributes->merge(['class' => 'ds-accordion']) }}>
    <div class="ds-accordion__container group" x-cloak>
        <button type="button" class="ds-accordion__button"
            x-on:click="accordion_selected !== '{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}' ? accordion_selected = '{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}' : accordion_selected = null"
            :class="accordion_selected == '{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}' ? '' : 'group-hover:bg-gray-50 duration-200'"
            data-accordion-item="{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}">
            
            <div class="ds-accordion__button--inner">
                <div class="flex items-center space-x-4 lg:space-x-6">
                   <div class="rounded-md bg-white flex flex-col flex-shrink-0 self-start text-center shadow-md lg:shadow-lg border border-gray-100 w-[60px] lg:w-[74px]">
                        <div class="text-sm lg:text-base rounded-t-md bg-red-850 text-white py-0.5">
                            {{ $item['month'] }}
                        </div>
                        <div class="pt-1 pb-1.5 text-blue-850">
                            <p class="text-2xl lg:text-3xl font-bold leading-none">{{ $item['date'] }}</p>
                            <p class="text-xs font-bold">{{ $item['day'] }}</p>
                        </div>
                    </div>
                    <div class="space-y-0.5 lg:space-y-2">
                        <span class="ds-accordion__button--inner--title">{{ $item['title'] }}</span>
                        @if(!empty($item['location']))
                            <span class="ds-accordion__button--inner--location">
                                <i class="fa-regular fa-location-dot"></i> {{ $item['location'] }}
                            </span>
                        @endif
                    </div>
                </div>
                
               <div class="ds-accordion__button--icon-container">
                    <i class="fa-regular fa-chevron-down ds-accordion__button--icon"
                        :class="accordion_selected == '{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}' ? 'transform ease-in-out rotate-180' : ''"></i>
                </div>
            </div>
        </button>

        <div class="ds-accordion__content" 
            x-ref="container_{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}"
            x-bind:style="accordion_selected == '{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}' ? 'max-height: ' + $refs.container_{{ Illuminate\Support\Str::slug($item['title'] ?? '', '_') }}.scrollHeight + 'px' : ''">
            <div class="ds-accordion__content--inner">
                <div class="grid lg:grid-cols-3 gap-6 lg:gap-8">
                        <div class="aspect-4/3">
                            <img src="{{ $item['image'] }}" alt="DBD Launch Lunch" class="w-full h-full object-cover object-top">
                        </div>
                        <div class="lg:col-span-2">
                            <ul>
                                <li>
                                    <i class="fa-regular fa-clock"></i>
                                    <p>{{ $item['time'] }}</p>
                                </li>
                                <li>
                                    <i class="fa-regular fa-ticket"></i>
                                    <p>{{ $item['tickets'] }}</p>
                                </li>        
                            </ul>
                            <div class="text-block">
                                {!! $item['details'] !!}
                            </div>
                            <x-button type="primary" href="{{ $item['btn-url'] }}" window_target="_blank" class="mt-4">
                                Buy Tickets
                            </x-button>
                        </div>
                </div>
            </div>
        </div>
    </div>
</div>
