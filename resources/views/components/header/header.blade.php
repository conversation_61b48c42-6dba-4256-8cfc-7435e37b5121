@aware(['page'])
<header x-data="Header" data-module="ds-header" {{ $attributes->merge(['class' => 'ds-header']) }} x-cloak>
    <nav class="nav-holder ds-site-padding">
        <div class="flex lg:flex-shrink-0 mx-auto md:ml-0 pl-[29px] md:pl-0">
            <a href="#home" class="-m-1.5 p-1.5 logo">
                <span class="sr-only">DBD 2026 Logo</span>
                <img class="master-logo" src="/img/dbd2026-master-logo.svg" alt="DBD 2026 Logo">
                <img class="white-logo" src="/img/dbd2026-secondary-logo.svg" alt="DBD 2026 Logo">
            </a>
        </div>

        <!-- Desktop Menu -->
        <ul class="hidden lg:flex lg:space-x-12 xl:space-x-16 desktop-menu">
            <li class="li group">
                <a href="#home" class="nav-link" data-target="home">Home
                <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#profile" class="nav-link" data-target="profile">
                    Profile
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#events" class="nav-link" data-target="events">
                    Events
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#charities" class="nav-link" data-target="charities">
                   Charities
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#merch" class="nav-link" data-target="merch">
                    Merch
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#sponsors" class="nav-link" data-target="sponsors">
                    Sponsors
                    <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
            <li class="li group">
                <a href="#contact" class="nav-link" data-target="contact">Contact
                <span class="underline group-hover:!w-full"></span>
                </a>
            </li>
        </ul>

        <div class="flex items-center lg:hidden">
            <x-hamburger-icon class="" />
        </div>
    </nav>

    <!-- Mobile Menu -->
    <div @keydown.window.escape="closeMenu()" x-show="menuopen" x-cloak
    class="fixed inset-0 z-50 overflow-hidden h-dvh lg:!hidden" aria-labelledby="slide-over-title" x-ref="dialog"
    aria-modal="true">
        <div class="absolute inset-0 overflow-hidden">
            <div x-transition:enter="ease-in-out duration-500" x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-500"
                x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                x-description="Background overlay, show/hide based on slide-over state."
                class="absolute inset-0 transition-opacity bg-black bg-opacity-70" x-on:click="menuopen = false"
                aria-hidden="true">
            </div>
            <div class="absolute inset-y-0 right-0 flex max-w-full md:pl-10">
                <div x-show="menuopen"
                    x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:enter-start="translate-x-full" x-transition:enter-end="translate-x-0"
                    x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
                    x-transition:leave-start="translate-x-0" x-transition:leave-end="translate-x-full"
                    class="w-screen max-w-full md:max-w-md"
                    x-description="Slide-over panel, show/hide based on slide-over state.">
                    <div class="flex flex-col h-full pt-6 pb-10 overflow-y-scroll shadow-xl bg-blue-850 md:pb-16">
                        <div class="relative flex-1 px-4 md:mt-16 sm:px-6">
                            <div class="absolute inset-0 flex flex-col justify-between px-4 sm:px-8">
                                <nav aria-label="Sidebar" class="flex flex-col h-full mobile-menu">
                                    <span class="sr-only">DBD 2026 Logo</span>
                                    <a href="#home" class="group" @click="closeMenu()"><img class="w-[90px] md:!hidden" src="/img/dbd2026-master-logo.svg" alt="DBD 2026 Logo"></a>
                                   <ul x-data="{ selected: null }">
                                        <li><a href="#home" class="group" @click="closeMenu()">Home</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#profile" class="group" @click="closeMenu()">Profile</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#events" class="group" @click="closeMenu()">Events</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#charities" class="group" @click="closeMenu()">Charities</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#merch" class="group" @click="closeMenu()">Merch</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#sponsors" class="group" @click="closeMenu()">Sponsors</a></li>
                                        <div class="divider"></div>
                                        <li><a href="#contact" class="group" @click="closeMenu()">Contact</a></li>
                                        <div class="divider"></div>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>