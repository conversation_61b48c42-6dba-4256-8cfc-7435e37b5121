
<x-layout :context="$__data">
    <div x-data="HomePage" class="ds-home">
        <section id="home" class="relative h-dvh bg-blue-850 overflow-hidden">
            <img src="/img/home-hero.jpg" alt="DBD Testimonial 2026" class="object-cover h-full w-full animate-zoom-in">
            
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center relative">
                    <div class="overflow-hidden">
                        <h1 class="animate-mask-fade-up opacity-0 animation-delay-200  transition-opacity text-white text-center text-5xl md:text-6xl lg:text-7xl uppercase font-heading_expanded font-bold italic tracking-wide drop-shadow-[0_2px_4px_rgba(0,0,0,0.55)]">Daniel <br class="sm:hidden"> Bell-Drummond</h1>
                        <h2 class="animate-mask-fade-up opacity-0 transition-opacity animation-delay-300 mt-2 block uppercase text-white font-heading_semiexpanded not-italic font-semibold tracking-[0.2em] text-xl md:text-2xl lg:text-3xl drop-shadow-[0_2px_4px_rgba(0,0,0,0.55)]">Testimonial 2026</h2>
                    </div>
                    {{-- <div class="overflow-hidden">
                        <p class="font-sans tracking-wider relative text-yellow-150 uppercase text-sm animate-mask-fade-up animation-delay-200 opacity-0 transition-opacity z-20">Est 2025</p>
                        <x-button data-fancybox="reservations" data-src="#reservation-modal" data-type="inline" type="secondary" class="md:!hidden mt-8 animate-mask-fade-up animation-delay-300 opacity-0 transition-opacity z-20">Book a table</x-button>
                        <p class="text-sm text-white font-sans bg-white/20 backdrop-blur-sm px-2 pt-0.5 pb-1 mt-6 rounded-md animate-mask-fade-up animation-delay-400 opacity-0 transition-opacity z-20 mx-4 sm:mx-6 lg:mx-8">We're opening on Sunday 30th March to help you celebrate Mother's Day.</p>
                    </div> --}}
                </div>
            </div>
            <div class="absolute bottom-8 flex items-center justify-center inset-x-0">
                <a href="#profile"><i class="fa-regular fa-chevron-down animate-bounce text-3xl lg:text-4xl text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.55)]"></i></a>
            </div>
        </section>

        <section id="profile" class="ds-section-padding bg-red-850 text-white relative">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <div class="grid md:grid-cols-3 gap-y-8">
                    @php
                        $slides = [
                            'images' => [
                                ["image" => "/img/profile/1.jpg", "alt" => "DBD2026 Pic 1"],
                                ["image" => "/img/profile/2.jpg", "alt" => "DBD2026 Pic 2"],
                                ["image" => "/img/profile/3.jpg", "alt" => "DBD2026 Pic 3"],
                                ["image" => "/img/profile/4.jpg", "alt" => "DBD2026 Pic 4"],
                                ["image" => "/img/profile/5.jpg", "alt" => "DBD2026 Pic 5"],
                                ["image" => "/img/profile/6.jpg", "alt" => "DBD2026 Pic 6"],
                                ["image" => "/img/profile/7.jpg", "alt" => "DBD2026 Pic 7"]
                            ]
                        ];
                    @endphp
                    <x-carousel.card-based 
                        :slides="$slides['images']"
                        :cardType="'card.image'"  
                        autoPlay
                        class="order-2 md:order-1 relative about-image-carousel aspect-3/4 overflow-hidden"
                    >
                        <div class="custom-nav">
                            <button class="button button--previous">
                                <i class="fa-thin fa-chevron-left"></i>
                            </button>
                            <button class="button button--next">
                                <i class="fa-thin fa-chevron-right"></i>
                            </button>
                        </div>
                    </x-carousel.card-based>
                    
                    <div class="order-1 md:order-2 md:col-span-2 md:pl-16 lg:pl-20 md:pt-0 text-block prose-p:text-white">
                        <p class="font-heading_expanded tracking-wider uppercase mb-2 mt-0">Profile</p>
                        <h1 class="font-heading text-white text-5xl lg:text-6xl uppercase mb-4">A Decade of Dedication</h1>
                        <p>Daniel Bell-Drummond is one of English cricket’s most gifted and graceful top-order batters. A proud product of the Kent Cricket Academy, he made his debut for the county as a teenager and has since become a cornerstone of the club’s batting lineup — combining classical strokeplay with calm authority at the crease.</p>

                        <p>With over a decade of service to Kent and contributions across formats, Daniel has earned a reputation not only for his consistency and leadership, but also for his style, humility, and deep love for the game. His impact extends beyond the field, as a role model and ambassador for diversity in cricket, and a passionate supporter of community and youth initiatives.</p>

                        <p>The DBD 2026 testimonial year celebrates Daniel’s outstanding career and the values he brings to the sport — professionalism, elegance, and an unwavering commitment to both cricket and community.</p> 
                    </div>
                </div>
            </div>
        </section>
        <section id="events" class="ds-section-padding bg-white md:pb-40 lg:pb-48">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <x-section-header 
                        title="Events" other_classes="text-white"
                        class="text-center max-w-2xl">
                        Sign up to our newsletter to receive news and updates as event details are announced or email <a href="mailto:<EMAIL>" class="font-bold"><EMAIL></a> for further info on any event.
                    </x-section-header>
                    @php
                        $accordionItems = [
                            [
                                'month' => 'Feb',
                                'date' => '12',
                                'day' => 'Thurs',
                                'title' => 'DBD Launch Lunch',
                                'location' => 'Mercure Hotel, Maidstone, Kent ME17 1RE',
                                'image' => '/img/profile/1.jpg',
                                'time' => '1pm',
                                'tickets' => '£80 per head / £800 per table of 10 / £900 per table of 9 + Kent /Sporting Legend',
                                'details' => '<p>Confirmed speakers are Richard Ellison, Chris Cowdrey and Matthew Fleming. Jamie Sutherland (comedian) will also be providing entertainment.</p>
                                <p>You can purchase your tickets online by clicking the button below. Dress code is lounge suits.</p>',
                                'btn-url' => 'https://events.kentcricket.co.uk/event/daniel-bell-drummond-testimonial-2026-launch-lun-4qvhvf'
                            ],
                            [
                                'month' => 'Aug',
                                'date' => 'TBC',
                                'day' => '-',
                                'title' => 'CRICKET MATCH IGGY XI v DBD XI',
                                'location' => 'Beckenham ',
                                'image' => '/img/profile/2.jpg',
                                'time' => 'TBC',
                                'tickets' => 'TBC',
                                'details' => 'Details of event to follow.',
                                'btn-url' => '#'
                            ],
                        ];
                    @endphp
                    <div x-data="{ accordion_selected: 'dbd_launch_lunch' }">
                        @foreach($accordionItems as $item)
                            <x-accordion :item="$item" />
                        @endforeach
                    </div>
                </div>
            </div>
        </section>

        <section id="charities" class="ds-section-padding bg-gray-100 md:pb-40 lg:pb-48">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <x-section-header 
                        title="Charities" outline_colour="blue" other_classes="text-gray-100"
                        class="text-center max-w-3xl">
                        As part of his 2026 testimonial year, DBD is proud to support two causes close to his heart: Platform Cricket and Cardiac Risk in the Young (CRY). Both charities reflect his passion for youth development and making a lasting impact beyond the boundary.
                    </x-section-header>
                    <div class="grid md:grid-cols-2 gap-y-8 gap-x-16 mt-10 lg:mt-12">
                        <div>
                            <div class="aspect-3/2 overflow-hidden">
                                <img src="/img/charity-platform-cricket.png" alt="Platform Cricket" class="w-full h-full object-cover" />
                            </div>
                            <h2 class="block text-3xl lg:text-5xl font-heading uppercase font-bold text-blue-850 mt-8 mb-4">Platform Cricket</h2>
                            <div class="text-block">
                                <p>Platform Cricket is a London-based registered charity that harnesses the power of cricket to support young people from disadvantaged and ethnic minority backgrounds across inner-city London.</p>
                                <p>Platform Cricket is more than just a sport provider—it’s a lifeline for young Londoners, opening doors to physical activity, social confidence, and structured support in communities where opportunity is limited.</p>
                            </div>
                            <x-button href="https://platform-ldn.org/" window_target="_blank" type="secondary" class="no-underline mt-5">Visit Website</x-button>  
                        </div>
                         <div>
                            <div class="aspect-3/2 overflow-hidden">
                                <img src="/img/charity-cry.png" alt="Cardiac Risk in the Young" class="w-full h-full object-cover" />
                            </div>
                            <h2 class="block text-3xl lg:text-5xl font-heading uppercase font-bold text-blue-850 mt-8 mb-4">Cardiac Risk in the Young</h2>
                            <div class="text-block">
                                <p>Cardiac Risk in the Young (CRY) is a UK charity dedicated to preventing young sudden cardiac death (YSCD)—tragic and often unexpected events affecting people aged 14–35. Across the UK, at least 12 apparently healthy young individuals lose their lives each week due to undiagnosed heart conditions, and 80% of these were without prior symptoms. CRY works to change that by combining awareness‑raising, proactive screening, medical research, and bereavement support.</p>
                            </div>
                            <x-button href="https://www.c-r-y.org.uk/" window_target="_blank" type="secondary" class="no-underline mt-5">Visit Website</x-button>  
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="merch" class="ds-section-padding bg-white md:pb-40 lg:pb-48">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <x-section-header 
                        title="Merch" other_classes="text-white"
                        class="text-center max-w-2xl">
                    </x-section-header>
                    <div class="grid md:grid-cols-3 gap-4 lg:gap-8 mt-10 lg:mt-12">
                        <div>
                            @php
                                $slides = [
                                    'images' => [
                                        ["image" => "/img/merch/navy-cotton-t.png", "alt" => "DBD Navy Cotton T Front"],
                                        ["image" => "/img/merch/navy-cotton-t-reverse.png", "alt" => "DBD Navy Cotton T Reverse"],
                                        ["image" => "/img/merch/light-blue-cotton-t.png", "alt" => "DBD Light Blue Cotton T Front"],
                                        ["image" => "/img/merch/light-blue-cotton-t-reverse.png", "alt" => "DBD Light Blue Cotton T Back"]
                                    ]
                                ];
                            @endphp
                            <x-carousel.card-based 
                                :slides="$slides['images']"
                                :cardType="'card.image'"  
                                class="merch-carousel aspect-square overflow-hidden bg-gray-100"
                            >
                                <div class="custom-nav">
                                    <button class="button button--previous">
                                        <i class="fa-thin fa-chevron-left"></i>
                                    </button>
                                    <button class="button button--next">
                                        <i class="fa-thin fa-chevron-right"></i>
                                    </button>
                                </div>
                            </x-carousel.card-based>
                            <div class="mt-4">
                                <p class="mb-2"><span class="font-bold text-blue-850 text-xl">Cotton T-Shirt</span> (2 colours)</p>
                                <p class="text-lg">Adults £12.00 / Juniors £10.00</p>
                                <x-button href="#" type="primary" class="mt-8" window_target="_blank">Buy Now</x-button>
                            </div>  
                        </div>
                        <div>
                            @php
                                $slides = [
                                    'images' => [
                                        ["image" => "/img/merch/light-blue-polo.png", "alt" => "DBD Light Blue Polo"],
                                        ["image" => "/img/merch/bergundy-polo.png", "alt" => "DBD Bergundy Polo"]
                                    ]
                                ];
                            @endphp
                            <x-carousel.card-based 
                                :slides="$slides['images']"
                                :cardType="'card.image'"  
                                class="merch-carousel aspect-square overflow-hidden bg-gray-100"
                            >
                                <div class="custom-nav">
                                    <button class="button button--previous">
                                        <i class="fa-thin fa-chevron-left"></i>
                                    </button>
                                    <button class="button button--next">
                                        <i class="fa-thin fa-chevron-right"></i>
                                    </button>
                                </div>
                            </x-carousel.card-based>
                            <div class="mt-4">
                                <p class="mb-2"><span class="font-bold text-blue-850 text-xl">Polo Shirt</span> (2 colours)</p>
                                <p class="text-lg">£12.00</p>
                                <x-button href="#" type="primary" class="mt-8" window_target="_blank">Buy Now</x-button>
                            </div>  
                        </div>
                        <div>
                            @php
                                $slides = [
                                    'images' => [
                                        ["image" => "/img/merch/blue-cap.png", "alt" => "DBD Blue Cap"],
                                        ["image" => "/img/merch/black-cap.png", "alt" => "DBD Black Cap"],
                                        ["image" => "/img/merch/bergundy-cap.png", "alt" => "DBD Bergundy Cap"],
                                    ]
                                ];
                            @endphp
                            <x-carousel.card-based 
                                :slides="$slides['images']"
                                :cardType="'card.image'"  
                                class="merch-carousel p-6 aspect-square overflow-hidden bg-gray-100"
                            >
                                <div class="custom-nav">
                                    <button class="button button--previous">
                                        <i class="fa-thin fa-chevron-left"></i>
                                    </button>
                                    <button class="button button--next">
                                        <i class="fa-thin fa-chevron-right"></i>
                                    </button>
                                </div>
                            </x-carousel.card-based>
                            <div class="mt-4">
                                <p class="mb-2"><span class="font-bold text-blue-850 text-xl">Caps</span> (3 colours)</p>
                                <p class="text-lg">£10.00</p>
                                <x-button href="#" type="primary"  class="mt-8" window_target="_blank">Buy Now</x-button>
                            </div>  
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="sponsors" class="ds-section-padding bg-gray-100 pb-32 md:pb-40 lg:pb-48 overflow-hidden">
            <div class="ds-site-padding ds-site-grid" data-aos="fade-up" data-aos-duration="1000">
                <div class="col-span-full lg:col-start-2 lg:col-span-10">
                    <x-section-header 
                        title="Sponsors" outline_colour="blue" other_classes="text-gray-100"
                        class="text-center max-w-3xl">
                        Daniel extends his heartfelt thanks to all testimonial sponsors for their generous support. Your backing makes this year possible and helps amplify the impact of both the celebrations and the chosen charities.
                    </x-section-header>
                </div>
            </div>
                @php
                    $slides = [
                        'sponsors' => [
                            ["image" => "img/sponsors/kent-ccc-logo.png", "alt" => "Kent CCC Logo", "url" =>"https://www.kentcricket.co.uk/"],
                            ["image" => "img/sponsors/bbc.png", "alt" => "BBCLogo", "url" =>"https://bbc.co.uk/"],
                            ["image" => "img/sponsors/kent-ccc-logo.png", "alt" => "Kent CCC Logo", "url" =>"https://www.kentcricket.co.uk/"],
                            ["image" => "img/sponsors/bbc.png", "alt" => "BBCLogo", "url" =>"https://bbc.co.uk/"],
                            ["image" => "img/sponsors/kent-ccc-logo.png", "alt" => "Kent CCC Logo", "url" =>"https://www.kentcricket.co.uk/"],
                            ["image" => "img/sponsors/bbc.png", "alt" => "BBCLogo", "url" =>"https://bbc.co.uk/"],
                            ["image" => "img/sponsors/kent-ccc-logo.png", "alt" => "Kent CCC Logo", "url" =>"https://www.kentcricket.co.uk/"],
                            ["image" => "img/sponsors/bbc.png", "alt" => "BBCLogo", "url" =>"https://bbc.co.uk/"]
                        ]
                    ];
                @endphp
                <x-carousel.card-based 
                    :slides="$slides['sponsors']"
                    :cardType="'logo'" 
                    contain="false"
                    wrapAround="true" 
                    groupCells="1,2,3"
                    autoPlay
                    pageDots
                    class="sponsors-carousel" 
                    data-aos="fade-up" 
                    data-aos-duration="1000"
                >
                </x-carousel.card-based>
        </section>
            {{-- Reservation Modal --}}
            <div id="reservation-modal" class="hidden max-w-4xl !bg-white border shadow">
                    <script type='text/javascript' src='//www.opentable.co.uk/widget/reservation/loader?rid=375906&type=standard&theme=tall&color=1&dark=false&iframe=true&domain=couk&lang=en-GB&newtab=false&ot_source=Restaurant%20website&cfe=true'></script>
            </div>
            {{-- End of Modal --}}
    </div>
</x-layouts.app>