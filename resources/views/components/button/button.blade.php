@props([
    'type' => 'primary',        // Styling variant
    'icon_prepend' => null,     // Optional left icon
    'icon_append' => null,      // Optional right icon
    'image_append' => null,     // Optional right image
    'href' => null,             // External/internal link
    'alpineHref' => null,       // Alpine-powered link
    'target' => null,           // Livewire wire:target
    'window_target' => '_self', // <a> target
])

@if($href || $alpineHref)
    {{-- Render as <a> --}}
    <a 
        href="{{ $href ?? '#' }}" 
        @if($alpineHref) x-bind:href="{{ $alpineHref }}" @endif
        target="{{ $window_target }}"
        data-module="ds-button"
        {{ $attributes->merge(['class' => 'ds-button group ds-button--' . $type]) }}
        wire:loading.attr="disabled"
    >
@else
    {{-- Render as <button> --}}
    <button 
        type="button"
        data-module="ds-button"
        {{ $attributes->merge(['class' => 'ds-button group ds-button--' . $type]) }}
        wire:loading.attr="disabled"
    >
@endif

    {{-- Prepend Icon --}}
    @isset($icon_prepend)
        <div class="ds-button__icon-container">
            <span class="relative z-10">
                <i class="{{ $icon_prepend }}"></i>
            </span>
        </div>
    @endisset

    {{-- Optional appended image (before text) --}}
    @isset($image_append)
        <img src="{{ $image_append }}" class="w-24 mr-2 -mt-0.5 ds-button__image-append" alt="">
    @endisset

    {{-- Button text --}}
    <span class="relative z-10" wire:loading.remove {{ $target ? "wire:target={$target}" : '' }}>
        {{ $slot }}
    </span>

    {{-- Loading state (if Livewire target set) --}}
    @if($target)
        <span class="relative z-10 flex items-center space-x-2" wire:loading {{ $target ? "wire:target={$target}" : '' }}>
            <i class="fas fa-spinner fa-spin"></i>
            <span>Please wait...</span>
        </span>
    @endif

    {{-- Append Icon --}}
    @isset($icon_append)
        <div class="ds-button__icon-container">
            <span class="relative z-10">
                <i class="{{ $icon_append }}"></i>
            </span>
        </div>
    @endisset

@if($href || $alpineHref)
    </a>
@else
    </button>
@endif
